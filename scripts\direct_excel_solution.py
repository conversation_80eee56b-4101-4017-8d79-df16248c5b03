"""
直接Excel解决方案
绕过智能体问题，直接处理Excel操作
"""
import sys
import os
import re
from pathlib import Path

# 设置编码
os.environ['PYTHONIOENCODING'] = 'utf-8'

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def safe_print(text):
    try:
        print(text, flush=True)
    except:
        print("[编码处理] 响应包含中文字符", flush=True)

def parse_command(text):
    """解析用户指令"""
    # 匹配格式: 客户ID为XXX,货品ID为XXX,价格为XXX,开始执行脚本
    pattern = r'客户ID为([^,，\s]+)[,，\s]*货品ID为([^,，\s]+)[,，\s]*价格为([0-9.]+)[,，\s]*开始执行脚本'
    match = re.search(pattern, text)
    
    if match:
        customer_id = match.group(1).strip()
        product_id = match.group(2).strip()
        price = float(match.group(3).strip())
        return customer_id, product_id, price
    
    return None

def execute_excel_operation(customer_id, product_id, price):
    """执行Excel操作"""
    try:
        from data_agent.tools.excel_writer import create_excel_writer_tool
        
        tool = create_excel_writer_tool()
        result = tool._run(
            customer_id=customer_id,
            product_id=product_id,
            agreement_price=price
        )
        
        safe_print("✅ 协议价数据已成功写入Excel文件！")
        safe_print(f"📋 写入详情：")
        safe_print(f"• 客户ID：{customer_id}")
        safe_print(f"• 货品ID：{product_id}")
        safe_print(f"• 协议价：{price}")
        safe_print(f"• 文件：data/raw/底表.xlsx")
        safe_print(f"• 工作表：协议价集合")
        
        return True
        
    except Exception as e:
        safe_print(f"❌ Excel操作失败: {e}")
        return False

def check_excel_data():
    """检查Excel数据"""
    try:
        import pandas as pd
        df = pd.read_excel("data/raw/底表.xlsx", sheet_name="协议价集合")
        safe_print(f"📊 当前数据行数: {len(df)}")
        
        if len(df) > 0:
            latest = df.iloc[-1]
            safe_print(f"最新记录: 客户ID={latest['客户ID(必填)']}, 货品ID={latest['货品ID(必填)']}, 价格={latest['协议价(必填)']}")
        
        return len(df)
    except Exception as e:
        safe_print(f"❌ 检查数据失败: {e}")
        return 0

def process_user_input(user_input):
    """处理用户输入"""
    safe_print(f"💬 输入: {user_input}")
    
    # 解析指令
    parsed = parse_command(user_input)
    if parsed:
        customer_id, product_id, price = parsed
        safe_print(f"🔍 解析结果: 客户ID={customer_id}, 货品ID={product_id}, 价格={price}")
        
        # 执行Excel操作
        success = execute_excel_operation(customer_id, product_id, price)
        if success:
            safe_print("🎉 操作完成！")
            return True
        else:
            return False
    else:
        safe_print("❌ 指令格式不正确")
        safe_print("正确格式: 客户ID为XXX,货品ID为XXX,价格为XXX,开始执行脚本")
        return False

def main():
    """主函数"""
    safe_print("🚀 直接Excel解决方案")
    safe_print("=" * 50)
    safe_print("解决编码问题 + 确保Excel操作成功")
    safe_print("=" * 50)
    
    # 显示当前状态
    check_excel_data()
    
    # 测试您的指令
    test_command = "客户ID为001,货品ID为002,价格为1,开始执行脚本"
    safe_print(f"\n🧪 测试指令: {test_command}")
    
    before_count = check_excel_data()
    success = process_user_input(test_command)
    after_count = check_excel_data()
    
    if success and after_count > before_count:
        safe_print("✅ 测试成功！数据已写入")
    else:
        safe_print("❌ 测试失败")
    
    # 交互模式
    safe_print("\n🎯 交互模式")
    safe_print("输入指令格式: 客户ID为XXX,货品ID为XXX,价格为XXX,开始执行脚本")
    safe_print("输入 'quit' 退出")
    safe_print("-" * 50)
    
    while True:
        try:
            user_input = input("\n用户: ").strip()
            if user_input.lower() in ['quit', 'exit', '退出']:
                safe_print("👋 再见！")
                break
            
            if not user_input:
                continue
            
            before_count = check_excel_data()
            success = process_user_input(user_input)
            after_count = check_excel_data()
            
            if success and after_count > before_count:
                safe_print("✅ 数据已成功写入Excel文件！")
            elif not success:
                safe_print("❌ 操作失败，请检查指令格式")
            
        except KeyboardInterrupt:
            safe_print("\n👋 再见！")
            break
        except Exception as e:
            safe_print(f"❌ 错误: {e}")

if __name__ == '__main__':
    main()
