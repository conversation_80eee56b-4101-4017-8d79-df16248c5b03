#!/usr/bin/env python3
"""
外部人员管理数据生成脚本（独立版本）

这个脚本是原始generate_external_personnel.py的独立版本，
不依赖于可能触发数据库连接的模块，专门用于Excel数据处理。
"""

import pandas as pd
import argparse
import os
import sys
import time
import psutil
from pathlib import Path


def kill_excel_processes():
    """终止所有Excel进程"""
    for proc in psutil.process_iter(['name']):
        try:
            if proc.info['name'] and 'excel' in proc.info['name'].lower():
                proc.kill()
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    time.sleep(1)


def remove_file_with_retry(file_path, max_retries=3):
    """带重试机制的文件删除"""
    for attempt in range(max_retries):
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
            return True
        except Exception as e:
            print(f"Attempt {attempt + 1} failed to remove {file_path}: {str(e)}")
            if attempt < max_retries - 1:
                kill_excel_processes()
                time.sleep(2)
            else:
                print(f"Failed to remove {file_path} after {max_retries} attempts")
                return False


def safe_excel_write(df, file_path, sheet_name='Sheet1', max_retries=3):
    """安全的Excel写入，带重试机制"""
    for attempt in range(max_retries):
        try:
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name=sheet_name)
            return True
        except PermissionError:
            if attempt < max_retries - 1:
                kill_excel_processes()
                time.sleep(2)
            else:
                raise
    return False


def convert_to_xls(xlsx_file, xls_file, overwrite=False):
    """将xlsx文件转换为xls格式"""
    try:
        if os.path.exists(xls_file):
            if not overwrite:
                print(f"目标文件{xls_file}已存在，跳过转换")
                return False
            else:
                kill_excel_processes()
                remove_file_with_retry(xls_file)
        
        df = pd.read_excel(xlsx_file)
        
        try:
            import xlwt
            workbook = xlwt.Workbook()
            sheet = workbook.add_sheet('Sheet1')
            
            # 写入列标题
            for col_num, column in enumerate(df.columns):
                sheet.write(0, col_num, column)
            
            # 写入数据
            for row_num, row in df.iterrows():
                for col_num, value in enumerate(row):
                    if pd.isna(value) or str(value).startswith('#') and str(value).endswith('!'):
                        sheet.write(row_num+1, col_num, '')
                    else:
                        sheet.write(row_num+1, col_num, value)
            
            workbook.save(xls_file)
            print(f"成功保存为Excel 2003格式: {xls_file}")
            
            # 转换成功后删除原xlsx文件
            try:
                os.remove(xlsx_file)
                print(f"已删除临时文件: {xlsx_file}")
            except Exception as e:
                print(f"删除临时文件失败: {str(e)}")
            return True
            
        except ImportError:
            print("xlwt模块未安装，无法转换为.xls格式")
            return False
            
    except Exception as e:
        print(f"转换失败: {str(e)}")
        return False


def process_external_personnel(base_file_path):
    """处理外部人员数据的核心逻辑"""
    print("开始处理外部人员数据...")
    
    # 读取底表.xlsx中的各个sheet
    template_df = pd.read_excel(base_file_path, sheet_name='外部人员模板')
    team_list_df = pd.read_excel(base_file_path, sheet_name='团队清单列表')
    area_price_df = pd.read_excel(base_file_path, sheet_name='区域价格')
    personnel_df = pd.read_excel(base_file_path, sheet_name='人员管理')
    business_unit_df = pd.read_excel(base_file_path, sheet_name='业务单元管理')
    area_mapping_df = pd.read_excel(base_file_path, sheet_name='分配区域')
    print("✓ 数据加载完成")

    # 1. 设置拼音为空，出生日期为2000-01-01
    template_df['拼音'] = ''
    template_df['出生日期 格式:yyyy-mm-dd'] = '2000-01-01'
    template_df['失效日期（必填）格式:yyyy-mm-dd'] = '2099-01-01'
    print("✓ 基础字段处理完成")

    # 2. 处理团队ID匹配 (DDLNAME + ENTITYID → DDLID)
    team_mapping = team_list_df.set_index(['DDLNAME', 'ENTITYID'])['DDLID'].sort_index()
    
    def get_team_id(ddlname, entityid=None):
        try:
            result = team_mapping.loc[(ddlname, entityid)]
            return result
        except (KeyError, TypeError):
            return ddlname  # 回退到原值
    
    if '独立单元ID(默认当前独立单元)' not in template_df.columns:
        template_df['独立单元ID(默认当前独立单元)'] = 1
        
    template_df['团队ID（必填）'] = template_df.apply(
        lambda row: get_team_id(row['团队ID（必填）'], row['独立单元ID(默认当前独立单元)']), axis=1)
    print("✓ 团队ID处理完成")

    # 3. 处理区域ID匹配
    area_mapping = area_price_df.set_index('DDLNAME')['DDLID'].to_dict()
    template_df['区域ID（必填）'] = template_df['区域ID（必填）'].apply(
        lambda x: area_mapping.get(x, x))
    print("✓ 区域ID处理完成")

    # 4. 处理默认字段
    if '独立单元ID(默认当前独立单元)' in template_df.columns:
        template_df['独立单元ID(默认当前独立单元)'] = template_df['独立单元ID(默认当前独立单元)'].apply(
            lambda x: 1 if pd.isna(x) or x not in [1, 5] else x)
    
    if '业务单元ID（必填）' not in template_df.columns:
        template_df['业务单元ID（必填）'] = 1
    else:
        template_df['业务单元ID（必填）'] = template_df['业务单元ID（必填）'].fillna(1)

    # 检查并设置所有可能缺失的字段
    required_fields = {
        '业务员ID': '',
        '制单人ID（必填）': '',
        '角色类别ID（必填）1-经销商 2-厂家 3-管理员 4-流向人员': 1,
        '行政区域ID（必填）': '',
        '业务单元ID（必填）': ''
    }
    
    for field, default in required_fields.items():
        if field not in template_df.columns:
            template_df[field] = default
        elif pd.api.types.is_numeric_dtype(template_df[field]):
            template_df[field] = template_df[field].fillna(default)
        else:
            template_df[field] = template_df[field].fillna('')
    print("✓ 默认字段处理完成")

    # 5. 处理员工映射
    if all(col in personnel_df.columns for col in ['EMPLOYEENAME', 'EMPLOYEEID', 'ENTRYID']):
        sales_mapping = personnel_df.set_index(['EMPLOYEENAME', 'ENTRYID'])['EMPLOYEEID'].sort_index()
        
        def get_employee_id(name, entryid=None):
            if pd.isna(name):
                return ''
            if str(name).isdigit():
                return str(name)
            try:
                result = sales_mapping.loc[(str(name).strip().upper(), entryid)]
                if isinstance(result, pd.Series):
                    return str(result.iloc[0])
                return str(result)
            except (KeyError, TypeError):
                return ''
        
        template_df['业务员ID'] = template_df.apply(
            lambda row: get_employee_id(row['业务员ID'], row['独立单元ID(默认当前独立单元)']), axis=1)
        
        template_df['制单人ID（必填）'] = template_df.apply(
            lambda row: get_employee_id(row['制单人ID（必填）'], row['独立单元ID(默认当前独立单元)']), axis=1)
    print("✓ 员工映射处理完成")

    # 6. 处理角色验证
    if '角色类别ID（必填）1-经销商 2-厂家 3-管理员 4-流向人员' in template_df.columns:
        valid_roles = {1, 2, 3, 4}
        template_df['角色类别ID（必填）1-经销商 2-厂家 3-管理员 4-流向人员'] = template_df[
            '角色类别ID（必填）1-经销商 2-厂家 3-管理员 4-流向人员'].apply(
            lambda x: x if x in valid_roles else 1)
    print("✓ 角色验证处理完成")

    # 7. 处理行政区域
    area_name_to_id = area_mapping_df.set_index('AREANAME')['SEQID'].to_dict()
    
    def get_area_id(area_name):
        if pd.isna(area_name):
            return 61
        return area_name_to_id.get(str(area_name).strip(), 61)
    
    template_df['行政区域ID（必填）'] = template_df['行政区域ID（必填）'].apply(get_area_id)
    print("✓ 行政区域处理完成")

    # 8. 处理业务单元
    if all(col in business_unit_df.columns for col in ['ZX_BUSINESS_UNIT', 'UNITID', 'ENTRYID']):
        unit_mapping = business_unit_df.set_index(['ZX_BUSINESS_UNIT', 'ENTRYID'])['UNITID'].sort_index()
        
        def get_unit_id(name, entryid=None):
            if pd.isna(name):
                return ''
            if str(name).isdigit():
                return name
            try:
                result = unit_mapping.loc[(str(name).strip(), entryid)]
                if isinstance(result, pd.Series):
                    return result.iloc[0]
                return result
            except (KeyError, TypeError):
                return name
        
        template_df['业务单元ID（必填）'] = template_df.apply(
            lambda row: get_unit_id(row['业务单元ID（必填）'], row['独立单元ID(默认当前独立单元)']), axis=1)
    print("✓ 业务单元处理完成")

    print("所有数据处理完成！")
    return template_df


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='生成外部人员管理数据')
    parser.add_argument(
        '--input', 
        default='data/raw/底表.xlsx',
        help='输入的底表Excel文件路径 (默认: data/raw/底表.xlsx)'
    )
    parser.add_argument(
        '--output-dir',
        default='data/processed',
        help='输出目录 (默认: data/processed)'
    )
    parser.add_argument(
        '--format',
        choices=['xlsx', 'xls', 'both'],
        default='both',
        help='输出格式 (默认: both)'
    )
    
    args = parser.parse_args()
    
    # 检查输入文件是否存在
    input_file = Path(args.input)
    if not input_file.exists():
        print(f"错误：输入文件不存在: {input_file}")
        return 1
    
    # 确保输出目录存在
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        # 处理数据
        result_df = process_external_personnel(str(input_file))
        
        # 保存结果
        output_base = output_dir / "外部人员管理"
        
        if args.format in ['xlsx', 'both']:
            xlsx_file = f"{output_base}.xlsx"
            if safe_excel_write(result_df, xlsx_file, '外部人员管理'):
                print(f"✓ Excel文件已保存: {xlsx_file}")
            else:
                print(f"✗ Excel文件保存失败: {xlsx_file}")
                return 1
        
        if args.format in ['xls', 'both']:
            xlsx_file = f"{output_base}.xlsx"
            xls_file = f"{output_base}.xls"
            
            # 如果需要xls格式但没有xlsx文件，先创建xlsx
            if args.format == 'xls' and not Path(xlsx_file).exists():
                if not safe_excel_write(result_df, xlsx_file, '外部人员管理'):
                    print(f"✗ 临时Excel文件创建失败: {xlsx_file}")
                    return 1
            
            # 转换为xls格式
            if convert_to_xls(xlsx_file, xls_file, overwrite=True):
                print(f"✓ XLS文件已保存: {xls_file}")
            else:
                print(f"✗ XLS文件转换失败: {xls_file}")
                return 1
        
        print("\n🎉 外部人员管理数据生成完成！")
        return 0
        
    except Exception as e:
        print(f"✗ 处理过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
